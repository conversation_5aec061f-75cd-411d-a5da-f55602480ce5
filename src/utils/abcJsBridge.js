//		AlipayJSBridgeReady 是掌银App H5容器自动注入的对象
export function abcJSBridgeReady(callback) {
  if (window.AlipayJSBridge) {
    callback	&& callback();
  } else {
    document.addEventListener('AlipayJSBridgeReady',	callback, false);
  }
}

// 打开窗口
// abcJSBridgeReady(function(){
//   AlipayJSBridge.call('pushWindow', {
//     url: 'https://enjoy.abchina.com/jf-web/epayItem?code=xxx',		// 要打开的页面的url
//     param : {
//       closeCurrentWindow: true, // 可选参数，打开新窗口的同时，关闭当前窗口，默认false
//     }
//   })
// })