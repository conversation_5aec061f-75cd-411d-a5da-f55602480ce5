<template>
  <view  :style="theme.style" class="">
    <view v-for="(item, index) in results" :key="index" class="order-lists">
      <view class="order-item">
        <!-- <view class="pickup-number">
          <view class="">取餐号：{{item.pickup_number}}</view>
          <view class="">{{formateStatus(item.making_type)}}</view>
        </view> -->
        <consume-item :infoItem="item" v-if="item.order_type == 0" />
        <recharge-item :infoItem="item" v-if="item.order_type == 3" />
        <withdrawal-item :infoItem="item" v-if="item.order_type == 2" />
        <refund-item :infoItem="item" v-if="item.order_type == 1" />
        <!-- 补卡订单，产品说是消费订单，但是后端给的order_type是5，单独抽出来吧 -->
        <repair-item :infoItem="item" v-if="item.order_type == 5" />
        <!-- 如果是称重 并且未支付昨天 -->
        <!--  v-if="item.order_status == 'ORDER_PAYING' && item.payment_order_type == 'buffet'" -->
        <view class="item-footer flex row-right col-center">
          <view class="tips flex flex-1"
            v-if="item.order_status == 'ORDER_PAYING' && item.payment_order_type == 'buffet' && downDiff(item) > 0">
            <u-count-down :time="downDiff(item)" format="mm:ss" @change="onDownChange"
              @finish="finishDown(item)"></u-count-down>
            后自动支付
          </view>
          <view class="tips flex flex-1" v-if="item.order_type == 0 && item.pickup_number">
            <view v-if="item.making_type === 'queues'">制作中</view>
            <view v-if="item.making_type === 'finish' && item.payment_order_type === 'goods_cashier'">已完成</view>
            <view v-if="item.making_type === 'finish'  && item.payment_order_type !== 'goods_cashier'">取餐号：{{ item.pickup_number }}</view>
            <view v-if="item.making_type === 'take_out'">已取餐</view>
          </view>
          <view class="m-l-10" v-if="item.order_status == 'ORDER_PAYING' && item.payment_order_type == 'buffet' && item.origin_fee !== 0">
            <u-button
              size="small"
              :disabled="finishTimeType"
              :customStyle="customBtnStyle"
              type="primary"
              text="去支付"
              @click="finishDown(item, 'manual')"
            ></u-button>
          </view>
          <view class="m-l-10 detail-btn">
            <u-button size="small" v-if="item.meal_scene && (item.meal_scene === 'HF' || item.meal_scene === 'WG' || item.meal_scene === 'WG_TRAY') && item.order_status === 'ORDER_FAILED'" @click="originalPriceDeducted(item)" :color="'#F8A73C'" :customStyle="customBtnStyle" type="primary">
              <span style="color: white">重新支付</span>
            </u-button>
          </view>
          <view class="m-l-10 appeal-btn" v-if="item.is_release_order">
            <u-button size="small" plain :customStyle="customBtnStyle" text="解绑" @click="untie(item)"></u-button>
          </view>
          <view class="m-l-10" v-if="item.is_apply_invoice">
            <u-button size="small" plain :customStyle="customBtnStyle" text="开发票"
              @click="gotoInvoicePath(item)"></u-button>
          </view>
          <view class="m-l-10 appeal-btn" v-if="item.can_appeal">
            <u-button size="small" plain :customStyle="customBtnStyle" text="申诉"
              @click="clickRefundPath(item)"></u-button>
          </view>
          <!-- <router-link class="m-l-20" to="/pages_bundle/payment/payment"> -->
          <!-- <u-button size="small" type="primary" text="手动支付"></u-button> -->
          <!-- </router-link> -->
          <!-- is_evaluation true 可以评价 -->
          <view class="m-l-10" v-if="item.is_evaluation">
            <u-button size="small" @click="gotoEvaluation(item)" :color="variables.colorPrimary" :customStyle="customBtnStyle"
              type="primary" text="评价"></u-button>
          </view>
          <!-- 申请退款-->
          <view class="m-l-10 refund-btn" v-if="item.order_type == 3 && item.order_status == 'ORDER_SUCCESS' && isShowBtn(item)">
            <u-button size="small" @click="gotoRefundDetails(item)" color="#EFF3F4" :customStyle="customBtnStyle"
              type="primary" text="申请退款"></u-button>
          </view>
          <!-- 取消申请退款-->
          <view class="m-l-10 red-btn" v-if="item.order_type == 3 && item.order_status == 'APPEAL_PENDING'">
            <u-button size="small" @click="handlerRefundDialog(item)" color="#EFF3F4" :customStyle="customBtnStyle"
              type="primary" text="取消申请"></u-button>
          </view>
          <!-- ORDER_FAILED -->
          <view class="m-l-10" v-if="item.payment_order_type === 'instore' && item.is_offline && item.order_status === 'ORDER_PAYING'">
            <u-button size="small" type="primary" :color="variables.colorPrimary" :customStyle="customBtnStyle" text="去支付"
              @click="showModalHandle('offline_paying', item)"></u-button>
          </view>
          <!-- 👨只有美团 才有去退款 -->
          <view class="m-l-10 appeal-btn" v-if="item.payment_order_type === 'mei_tuan' && item.order_status === 'ORDER_SUCCESS'">
            <u-button size="small" plain :customStyle="customBtnStyle" text="去退款"
              @click="getThirdMeituan(item)"></u-button>
          </view>
          <!-- 消费成功订单，美团没有详情按钮 -->
          <view class="m-l-10 detail-btn">
            <u-button size="small" @click="gotoDetails(item)" color="#EFF3F4" :customStyle="customBtnStyle" type="primary"
              text="详情"></u-button>
          </view>
          <!-- 	<router-link class="m-l-20" to="/pages_order/order/order_detail?type=consume">
          </router-link> -->
        </view>
      </view>

    </view>
    <u-modal :show="showModal" :title="modalTitle" confirmColor="#11e6c3" :showCancelButton="true" @confirm="confirmHandle"
      @cancel="cancelHandle">
      <view class="md text-center">{{ modalContent }}</view>
    </u-modal>
    <u-modal :show="isShowCancel" title="取消申请" content=' ' confirmText='确认取消' cancelText="我再想想" confirmColor="#FE5858"
      cancelColor='#11E69E' :showCancelButton="true" @cancel="isShowCancel = false" @confirm="handlerCancelOrder">
      <view class="md text-center">确认取消当前订单的申请吗？</view>
    </u-modal>
  </view>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import consumeItem from './consume/item.vue'
import rechargeItem from './recharge/item.vue'
import withdrawalItem from './withdrawal/item.vue'
import refundItem from './refund/item.vue'
import repairItem from './repair/item.vue'
import { to, replaceDate } from '@/utils/util'
import { apiBookingRechargeChargeApprovalCancel, apiBookingRechargeChargeWithdrawRefundOn, apiBookingUserOrderOfflineOrderPay,getApiLoginOrderDetail } from '@/api/order'
import { getApiorBuffOeterPaying } from '@/api/buffet'
import { getApiMeituanLoginFree } from '@/api/app'
export default {
  props: {
    results: Array,
    startTime: String,
    endTime: String
  },
  components: {
    consumeItem,
    rechargeItem,
    withdrawalItem,
    refundItem,
    repairItem
  },
  data() {
    return {
      timeData: {},
      finishTimeType: false,
      customBtnStyle: {
        minWidth: '100rpx',
        height: '50rpx',
        lineHeight: '60rpx',
      },
      showModal: false, // 是否显示弹窗
      modalTitle: '提示', // 弹窗标题
      modalType: '', // 弹窗类型
      modalContent: '如对应补贴已清零，退款后，相应金额也将被清零，是否继续？', // 弹窗内容
      modalInfo: {}, // 弹窗的携带数据
      isShowCancel: false, // 是否显示取消申请
      refundTipTxt1: "现金充值订单暂不支持线上退款，如需退款请联系管理员",
      refundTipTxt2: "当前订单超过可退款时间，如需退款请联系管理员",
      retfundData: {}  // 退款点击的项目
    }
  },
  created() {},
  mounted() {
  },
  methods: {
    ...mapActions({
      setOrderDetailsInfo: 'setOrderDetailsInfo'
    }),
    // 时间兼容
    replaceDate(date) {
      date = date.toString()
      return date.replace(new RegExp(/-/gm), '/')
    },
    downDiff(item) {
      let nowTime = new Date().getTime()
      let updateTime = item.update_time ? new Date(this.replaceDate(item.update_time)).getTime() : 0
      let time = 0
      if (item.order_status == 'ORDER_PAYING' && nowTime < (updateTime + item.auto_pay_order_time * 1000)) {
        time = updateTime + item.auto_pay_order_time * 1000 - nowTime
      } else {
        time = 0
      }
      // 0.003 * 60 * 60 * 1000
      return time
    },
    // 倒计时结束
    finishDown(data, type) {
      let that = this
      if (type == 'manual') {
        uni.showModal({
          title: '支付',
          content: '是否确定支付',
          success: function (res) {
            if (res.confirm) {
              that.$emit('finishDown', data)
            } else if (res.cancel) {
              // console.log('用户点击取消')
            }
          }
        })
      } else {
        // 如果需要倒计时结束调支付接口 就放开，因为后端倒计时结束会自动调，相差一两分钟
        // 禁止调手动支付接口
        this.finishTimeType = true
        // 后台支付的话，你直接刷新列表就行了
        setTimeout(() => {
          that.$emit('updateList', true)
        }, 300);
        //this.$emit('finishDown', data)
      }
    },
    onDownChange(e) {
      // console.log(e)
      // this.timeData = e
    },
    gotoDetails(data) {
      if (data.payment_order_type === 'mei_tuan') {
        return this.getLoginOrderDetail(data)
      }
      let orderType = ''
      switch (data.order_type) {
        case 0:
          orderType = 'consume'
          break
        case 1:
          orderType = 'refund'
          break
        case 2:
          orderType = 'withdrawal'
          break
        case 3:
          orderType = 'recharge'
          break
        case 5:
          orderType = 'repair'
          break
      }
      this.setOrderDetailsInfo(data)
      var urlPath = '/pages_order/order/order_detail'
      // 20240301 跳转充值类订单跳新页面
      if (orderType === 'recharge') {
        urlPath = '/pages_order/order/refund_details'
      }
      this.$miRouter.push({
        query: {
          type: orderType,
          id: data.id,
          orderRefundId: orderType == 'refund' ? data.order_refund_id : '',
          // data: this.$encodeQuery(data), // 废弃
          startTime: this.startTime,
          endTime: this.endTime,
          flag: "detail"
        },
        path: urlPath
      })
      // /pages_bundle/order/order_detail?type=consume
    },
    clickRefundPath(data) {
      // 如果是补贴钱包的弹多个窗口提示
      if (data.is_subsidy_fee_order) {
        this.showModalHandle('appeal_subsidy', data)
        this.setOrderDetailsInfo(data)
      } else {
        this.setOrderDetailsInfo(data)
        this.$miRouter.push({
          query: {
            type: 'food'
          },
          path: '/pages_order/order/refund'
        })
      }
    },
    // 设置弹窗数据
    showModalHandle(type, data) {
      switch (type) {
        case 'appeal_subsidy': // 补贴申诉订单？
          this.modalType = type
          this.modalTitle = '提示'
          this.modalContent = '如对应补贴已清零，退款后，相应金额也将被清零，是否继续？'
          break;
        case 'offline_paying': // 离线待支付订单
          this.modalType = type
          this.modalTitle = '支付'
          this.modalContent = '是否确认支付'
          this.modalInfo = data
          break;
      }
      this.showModal = true
    },
    // 重置弹窗数据
    resetModalHandle() {
      this.modalType = ''
      this.modalTitle = ''
      this.modalContent = ''
      this.modalInfo = {}
    },
    gotoEvaluation(data) {
      this.setOrderDetailsInfo(data)
      this.$miRouter.push({
        query: {
          type: 'food',
          id: data.order_payment_id
        },
        path: '/pages_order/order/evaluate'
      })
    },
    confirmHandle() {
      if (this.modalType === 'appeal_subsidy') {
        this.$miRouter.push({
          query: {
            type: 'food'
          },
          path: '/pages_order/order/refund'
        })
        this.showModal = false
      }
      // 离线订单待支付状态走这
      if (this.modalType === 'offline_paying') {
        this.repayOfflineOrder(this.modalInfo)
      }
    },
    cancelHandle() {
      this.showModal = false
      this.resetModalHandle()
    },
    gotoInvoicePath(data) {
      this.$miRouter.push({
        query: {
          id: data.order_payment_id,
          o_id: data.organization_id,
          trade_no: data.trade_no,
          total_fee: data.pay_fee,
          invoice_content: data.total_payment_order_type_alias
        },
        path: '/pages_order/invoice/apply'
      })
    },
    formateStatus(status) {
      let text
      if (status === 'queues') {
        text = '制作中'
      } else if (status === 'finish') {
        text = '待取餐'
      } else if (status === 'take_out') {
        text = '已取餐'
      }
      return text
    },
    untie(item) {
      this.$emit('untie', item)
    },
    // 跳转申请退款
    async gotoRefundDetails(data) {
      // 先判断是否开启申请退款
      var flag = await this.checkIsOpenRefund(data)
      if (!flag) {
        return this.$u.toast('该组织未开启退款申请，请联系管理员')
      }
      if (!this.checkCanRefund(data)) {
        return
      }
      this.setOrderDetailsInfo(data)
      this.$miRouter.push({
        path: '/pages_order/order/refund_details',
        query: {
          flag: 'refund'
        }
      })
    },
    // 检查是否能申请退款 ， 优先判断是否为现金支付订单，再判断是否超过下单时间
    checkCanRefund(data) {
      // 类型
      let cashType = data.pay_scene || ''
      if (cashType === 'charge_offline') {
        this.$u.toast(this.refundTipTxt1)
        return false
      }
      // 下单时间
      // let createTime = data.create_time || ''
      // let times = new Date(replaceDate(createTime)).getTime()
      // let nowTimes = new Date().getTime()
      // console.log("nowTimes", nowTimes, times, nowTimes - times);
      // if ((nowTimes - times) > 1000 * 60 * 60 * 48) {
      //   this.$u.toast(this.refundTipTxt2)
      //   return false
      // }
      return true
    },
    // 取消申请
    handlerRefundDialog(data) {
      this.retfundData = data
      this.isShowCancel = true
    },
    // 确认取消
    async handlerCancelOrder() {
      this.isShowCancel = false
      var params = {
        trade_no: this.retfundData.trade_no
      }
      const [err, res] = await to(apiBookingRechargeChargeApprovalCancel(params))
      if (err) {
        this.$u.toast("取消订单失败")
        return
      }
      if (res && res.code === 0) {
        this.$u.toast("取消订单成功")
        this.$emit('updateList')
      } else {
        this.$u.toast("取消订单失败," + res.msg)
      }
    },
    // 是否显示申请按钮 判断是否当日充值的订单
    isShowBtn(data) {
      if (!data || typeof data !== 'object') {
        return false
      }
      let createTime = data.create_time || ''
      if (createTime) {
        let times = new Date(replaceDate(createTime))
        let nowTimes = new Date()
        if (
          times.getFullYear() === nowTimes.getFullYear() &&
          times.getMonth() === nowTimes.getMonth() &&
          times.getDate() === nowTimes.getDate()
        ) {
          return true
        }
      }
      return false
    },
    // 检测是否开启申请退款
    checkIsOpenRefund(data) {
      return new Promise((resolve) => {
        this.$showLoading({
          title: '请稍后....',
          mask: true
        })
        apiBookingRechargeChargeWithdrawRefundOn({ trade_no: data.trade_no }).then(res => {
          uni.hideLoading()
          if (res && res.code === 0) {
            var data = res.data || {}
            var flag = data.withdraw_refund_on || false
            resolve(flag)
          } else {
            resolve(false)
          }
        }).catch(err => {
          console.log("err", err);
          uni.hideLoading()
          resolve(false)
        })
      })
    },
    // 手动支付
    originalPriceDeducted(data) {
      getApiorBuffOeterPaying({
        order_id: data.id
      }).then(res => {
        if (res && res.code === 0) {
          this.$u.toast("支付成功")
          this.$emit('updateList')
        } else {
          this.$u.toast(res.msg)
        }
      })
    },
    // 离线订单重新发起支付
    async repayOfflineOrder() {
      this.$showLoading({
        title: '支付中...',
        mask: true
      })
      let params = {
        order_payment_id: this.modalInfo.order_payment_id
      }
      // await this.$sleep(800)
      const [err, res] = await to(apiBookingUserOrderOfflineOrderPay(params))
      uni.hideLoading()
      this.showModal = false
      // this.$emit('updateList')
      // return
      if (err) {
        this.$u.toast(err.message)
        return
      }
      if (res && res.code === 0) {
        this.$u.toast(res.msg || '支付成功')
        this.showModal = false
        this.resetModalHandle()
        this.$emit('updateList')
      } else {
        this.$u.toast('支付失败，请联系管理员：' + res.msg)
      }
    },
    // 美团获取链接
    async getThirdMeituan() {
      this.$showLoading({
        title: '请求中...',
        mask: true
      })
      let res = await getApiMeituanLoginFree()
      uni.hideLoading()
      if (res.code === 302) {
        if (res.data.url) {
          window.location.href = res.data.url
        }
      } else {
        uni.$u.toast(res.msg)
      }
    },
    // 订单跳详情
    async getLoginOrderDetail(item) {
      this.$showLoading({
        title: '请求中...',
        mask: true
      })
      let res = await getApiLoginOrderDetail({
        order_id: item.id
      })
      uni.hideLoading()
      if (res.code === 302) {
        if (res.data.url) {
          window.location.href = res.data.url
        }
      } else {
        uni.$u.toast(res.msg)
      }
    },
  }
}
</script>

<style lang="scss">
// @import '/item.scss';
.order-lists {
  padding: 0 40rpx;


  .order-item {
    background-color: #ffffff;
    padding: 0 40rpx;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    position: relative;

    // .pickup-number{
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   width: 100%;
    //   height: 100rpx;
    //   display: flex;
    //   justify-content: space-between;
    //   padding: 24rpx 30rpx;
    //   border-radius: 20rpx 20rpx 0 0;
    //   background: #e6fff3 linear-gradient(90deg, #a9fed5 0%, #ebfef5 0%, #ebfef5 0%, #d3fcf5 100%, #d3fcf5 100%);
    // }
    .item-goods {
      position: relative;

      .goods-scroll {
        height: 190rpx;
        white-space: nowrap;

        .goods-lists {
          padding: 20rpx 0 0;

          .goods-item {
            margin-right: 15rpx;
            width: 110rpx;
          }
        }
      }

      .goods-num {
        position: absolute;
        width: 150rpx;
        height: 110rpx;
        line-height: 110rpx;
        text-align: right;
        background: linear-gradient(90deg, rgba(256, 256, 256, 0) 0%, #ffffff 100%);
        right: 0;
        top: 20rpx;
        z-index: 10;
        opacity: 0.7;
      }
    }

    .tips {
      color: $color-yellow !important;
    }

    .item-footer {
      border-top: 1px dashed $border-color-base;
      height: 90rpx;
    }
  }

  .u-count-down .u-count-down__text {
    color: red;
  }

  .detail-btn .u-button {
    color: #000 !important;
  }

  .appeal-btn .u-button {
    border-color: $color-primary;
    color: $color-primary;
  }

  .refund-btn .u-button {
    background-color: $color-primary !important;
    color: #fff !important;
  }

  .red-btn .u-button {
    background-color: #FE5858 !important;
    color: #fff !important;
  }
}</style>
